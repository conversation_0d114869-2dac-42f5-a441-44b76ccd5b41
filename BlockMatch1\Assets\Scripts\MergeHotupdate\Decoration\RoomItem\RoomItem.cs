using System;
using System.Collections.Generic;
using UnityEngine;
using RoomItemId = System.String;
using RoomNodeId = System.Int64;
using RoomId = System.Int32;
using DecorationRom.Core;
using DragonPlus.Core;
using Framework;

public class RoomItem
{
    public enum Status
    {
        Lock = 0,
        UnLock = 1,
        Received,
    }
    
    private RoomItemGraphic _graphic;
    private RoomNode _node;
    private Home.Core.RoomItemCfg _graphicConfig;
    
    public string Id
    {
        get => _graphicConfig.id;
    }
    
    public string Controller
    {
        get { return _graphicConfig.animItemCfg.controller; }
    }

    public List<Home.Core.AnimItemEffect> Effects
    {
        get { return _graphicConfig.animItemCfg.effects; }
    }

    public Room room
    {
        get => _node.Room;
    }

    public Vector3 ScreenPos
    {
        get => _graphic.ScreenPos;
    }

    public Vector3 WorldPosition
    {
        get => _graphic.WorldPosition;
    }

    private Sprite _icon;
    public Sprite Icon
    {
        get => _icon;
    }
    
    public Vector2 Offset
    {
        get => _graphic.Offset;
    }

    public RoomNode Node
    {
        get => _node;
    }

    public bool IsOld
    {
        get { return _graphicConfig.oldFurItemCfg.isOld; }
    }

    public bool CanOneTimeChoice
    {
        get => _graphic.CanOneTimeChoice();
    }
    public RoomItem(Home.Core.RoomItemCfg viewConfig, RoomNode roomNode)
    {
        _graphicConfig = viewConfig;
        _node = roomNode;
        
        if (!viewConfig.oldFurItemCfg.isOld)
        {
            
            var iconName = Id.Split("###".ToCharArray());
            _icon = CoreUtils.GetSprite(string.Format("Icon{0}", _node.Room.ResId), $"{iconName[^1]}_2d_256", roomNode.transform.gameObject);
        }
    }

    public bool IsReady()
    {
        if (_graphic == null)
        {
            return false; 
        }
        return _graphic.IsReady();
    }

    private RoomNodeId parseNodeID(RoomItemId itemID)
    {
        return long.Parse(itemID.Split('_')[0]);
    }

    private bool checkParentNode(string parentItemId)
    {
        if (!string.IsNullOrEmpty(parentItemId))
        {
            //如果父节点不为空
            _node.ParentId = parseNodeID(parentItemId);
            if (!_node.Room.GetNode(_node.ParentId).CurrentItem.Id.Equals(parentItemId))
            {
                //检查父节点是否对
                return false;
            }
        }

        return true;
    }

    public void UpdateSpriteAndOffset()
    {
        var offset = Vector2.zero;
        foreach (var kv in _graphicConfig.offsetItemCfg.pos)
        {
            if (!checkParentNode(kv.key))
                continue;
            offset = kv.value;
        }

        if (_graphic == null)
        {
            _graphic = new RoomItemGraphic(this, offset);
        }
        else
        {
            _graphic.SetItemSprite(offset);
        }
    }

    public void PendingReceived()
    {
        UpdateSpriteAndOffset();
        OnChangeDifferentItem();
    }

    public void ClearGraphic()
    {
        if(_graphic == null)
            return;
        
        _graphic.Clear();
    }

    public void PlayCleanRoomAnim(Action animEnd = null)
    {
        if(_graphic == null)
            return;
        
        _graphic.PlayCleanRoomAnim(animEnd);
    }
    public void PlayEffect()
    {
        if(_graphic == null)
            return;
        
        _graphic.PlayEffect();
    }
    public void StopEffect()
    {
        if(_graphic == null)
            return;
        _graphic.StopEffect();
    }
    
    public void PlayAnim()
    {
        if(_graphic == null)
            return;
        
        _graphic.PlayAnim();
    }
    public void OnTap()
    {
        _graphic?.OnTap();
    }

    public void OnNormal()
    {
        _graphic?.OnNormal();
    }
    
    public void OnSelected()
    {
        _graphic?.OnSelected();
    }

    public void OnChangeDifferentItem(Action animEnd = null)
    {
        _graphic?.OnChangeDifferentItem(animEnd);
    }

    public void SetPlayEffect(bool playEffect)
    {
        if(_graphic != null)
            _graphic.SetPlayEffect(playEffect);
    }
    public float GetChangeDifferentItemLength()
    {
        return _graphic.GetChangeDifferentItemLength();
    }

    public void OnConfirm(Action animEnd = null)
    {
        if (_graphic == null)
        {
            CLog.Info("RoomItem.OnConfirm: _graphic is null, cannot perform confirm action.");
            return;
        }

        try
        {
            _graphic.OnConfirm(animEnd);
        }
        catch (Exception ex)
        {
            CLog.Error($"RoomItem.OnConfirm: An error occurred while confirming the item: {ex.Message}");
        }
    }

    public void OnDeSelect()
    {
        _graphic?.OnDeSelect();
    }
    public void SaveSelection()
    {
    }

    public bool TouchMe(Vector2 screenPos)
    {
        if (IsOld) 
            return false;
        
        return _graphic.TouchMe(screenPos);
    }
}