using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using UnityEngine;
using RoomId = System.Int32;
using RoomNodeId = System.Int64;
using TMGame;
using TMGame.Storage;
using DecorationRom.Core;
using DecorationRom;
using DecorationRom.Event;
using DragonPlus.Config.InGame;
using Gameplay.BI;

public partial class RoomManager : Manager<RoomManager>
{
    private const int roomChapter = 1004;
    private const int roomChapterB = 1005;

    private TableRoomChapter curRoomChapter = null;
    private Dictionary<int, Room> _roomsDic = new Dictionary<int, Room>();
    private int curCharpterId = roomChapter;
    private Room _currentInRoom = null;
    private bool isRoomReady = false;
    public int LastNodeId;
    private StorageRoomCommon storageHome = null;
    public StorageRoomCommon StorageHome { get { return storageHome; } }

    public delegate void EnterCall(bool isSuccess);

    public TableRoomChapter CurRoomChapter => curRoomChapter;
    public int RoomChapter => curCharpterId;
    public Room CurrentInRoom => _currentInRoom;
    public bool IsRoomReady { get => isRoomReady; private set => isRoomReady = value; }

    public void EnterDefaultRoom(EnterCall enterCall)
    {
        // UpdateAreaProgress(79021);
        // EnterRoom(79021, enterCall: enterCall);
    }

    public void EnterRoom(RoomId roomId, bool isInit = true, EnterCall enterCall = null)
    {
        if (GetRoomId() == roomId)
        {
            enterCall?.Invoke(true);
            IsRoomReady = true;
            return;
        }
        if (roomId == 70004)
        {
            // TODO FIX ERROR
            // BIHelper.SendGameEvent(BiEventCooking.Types.GameEventType.GameEventFteRoom2UnlockTap);
        }
        UpdateAtlasPathNodeList(roomId);
        RoomBI.SendDecoEvent_enter_room(roomId);
        IsRoomReady = false;
        GameGlobal.GetMod<ModCoroutine>().StartCoroutine(OnLoadRoom(roomId, isInit, enterCall));
    }

    private IEnumerator OnLoadRoom(RoomId roomId, bool isInit = true, EnterCall enterCall = null)
    {
        if (GetRoomId() == roomId)
        {
            IsRoomReady = true;
            enterCall?.Invoke(true);
            yield break;
        }

        Room room = GetRoom(roomId);
        if (room == null)
        {
            CLog.Error($"_roomId:{roomId} GetRoomError is null ");
            enterCall?.Invoke(false);
            yield break;
        }

        _currentInRoom?.ClearGraphic();
        _currentInRoom = room;
        _currentInRoom.InitGraphic(isInit);
        while (!_currentInRoom.IsCurrentRoomReady())
        {
            yield return new WaitForFixedUpdate();
        }

        IsRoomReady = true;
        enterCall?.Invoke(true);
        var roomConfig = TableConfigManage.Instance.GetTableRoom(roomId);
        if (roomConfig != null && !string.IsNullOrEmpty(roomConfig.soundId))
        {
            GameGlobal.GetMgr<SoundMgr>().PlayBgMusic(roomConfig.soundId);
        }
    }

    public void ExitRoom()
    {
        _currentInRoom?.ClearGraphic();
        _currentInRoom = null;
    }

    private Room GetRoom(RoomId roomId)
    {
        if (!_roomsDic.ContainsKey(roomId))
        {
            int resId = TableConfigManage.Instance.GetRoomResId(roomId);
            _roomsDic[roomId] = new Room(roomId, resId);
        }

        return _roomsDic[roomId];
    }

    private int GetRoomId() => _currentInRoom?.Id ?? -1;
    private int GetCurRoomId() => _currentInRoom?.Id ?? GetStorageRoomId();

    public int CurRoomId
    {
        get
        {
            if (storageHome == null)
                storageHome = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            if (storageHome.CurRoomId <= 0)
            {
                return GetCurRoomId();
            }
            return storageHome.CurRoomId;
        }
        set
        {
            storageHome ??= SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            storageHome.CurRoomId = value;
        }
    }

    public int GetLastRoomId()
    {
        int curRoomId = CurRoomId;
        if (curRoomId > 0 && IsCollectRoom(curRoomId) && IsCollectRoomOpen() && IsActiveRoom(curRoomId))
            return curRoomId;

        int lastRoomId = GetLastUnLockRoomId();
        return lastRoomId > 0 ? (curRoomId == lastRoomId ? curRoomId : lastRoomId) : curRoomId;
    }

    public void PlayCleanRoomAnim(TableRoomNode roomNode, Action animEnd = null)
    {
        _currentInRoom?.PlayCleanRoomAnim(roomNode, animEnd);
    }

    public void PlayEffect() => _currentInRoom?.PlayEffect();
    public void StopEffect() => _currentInRoom?.StopEffect();
    public void GetRoomNode(RoomNodeId id, bool change, bool playEffect) => _currentInRoom?.GetItem(id, change, playEffect);
    public void SetRoomNodeActive(int id, bool active) => _currentInRoom?.SetNodeActive(id, active);
    public void PlayAnim(int nodeId) => _currentInRoom?.PlayAnim(nodeId);
    public void ClearAllGraphic() => _currentInRoom?.ClearAllGraphic();
    public void ShowAllOldItem() => _currentInRoom?.ShowAllOldItem();
    public void Release()
    {
        _currentInRoom?.ClearGraphic();
        _currentInRoom = null;
        _roomsDic?.Clear();
        IsRoomReady = false;
    }

    public StorageRoom GetStorageRoom()
    {
        if (_currentInRoom == null)
            return null;

        return storageHome.RoomData.TryGetValue(GetRoomId(), out var room) ? room : null;
    }

    public void UpdateRoomNodeStatus()
    {
       var list =  StorageExtension.GameBlockLevelStorage.LevelPlayInfos;
       for (int i = 0; i < list.Count; i++)
       {
           var data = list[i];
           var configLvele = InGameConfigManager.GetChapterLevelInfo(data.levelId);
           if (configLvele == null || configLvele.RewardId < 0)
           {
               continue;
           }
           //不需要判断是否领过奖
           // if (!StorageExtension.GameBlockLevelStorage.RewardStatus.ContainsKey(configLvele.LevelId))
           // {
           //     continue;
           // }
           //

           if (data.gameResult != (int)EnumGameResultType.EFST_Victory)
           {
               continue;
           }
           StorageExtension.GameBlockLevelStorage.RewardStatus.TryAdd(configLvele.RewardId, 1);
           var cofReward = InGameConfigManager.GetChapterRewardConfig(configLvele.RewardId);
           if (cofReward == null || cofReward.UnlockRoomNodeId <= 0)
           {
               continue;
           }
           SetRoomNodeUnlockStaus(cofReward.UnlockRoomNodeId);
       }
       
    }


    public void UpdateRoomChapterStorage()
    {
        UpdateCollectRoomChapterStorage();
        storageHome ??= SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        if (storageHome == null)
            return;

        int chapterId =  storageHome.RoomChapterId;
        var storageGlobal = SDK<IStorage>.Instance.Get<StorageGlobal>();
        int fistAppVersionNum = int.Parse(storageGlobal.UserData.FirstAppVersion.Replace("v", ""));
        if ( storageHome.RoomChapterId <= 0)
        {
            if (fistAppVersionNum > 5 )
            {
                chapterId = roomChapterB;
            }
            else
            {
                chapterId = roomChapter;
            }
        }

        
        curRoomChapter = null;
        curCharpterId = chapterId;

        ReplaceRoomNode(false);
        var listRoomChapters = TableConfigManage.Instance.GetRoomChapters();
        if (listRoomChapters == null || listRoomChapters.Count == 0)
            return;

        curRoomChapter = listRoomChapters.FirstOrDefault(kv => kv.id == chapterId) ?? listRoomChapters[listRoomChapters.Count - 1];

        if (storageHome.CurRoomId <= 0)
        {
            CurRoomId = GetFirstRoomId();
            SaveUnLockStorageRoom(CurRoomId);
            return;
        }

        int firstRoomId = GetFirstRoomId();
        if (!storageHome.UnLockRoom.ContainsKey(firstRoomId))
            SaveUnLockStorageRoom(firstRoomId);
    }

    public int GetStorageRoomId()
    {
        storageHome ??= SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        if (storageHome == null)
            return -1;

        return storageHome.CurRoomId > 0 ? storageHome.CurRoomId :GetFirstRoomId();
    }

    public int GetFirstRoomId() => curRoomChapter?.roomIds?.FirstOrDefault() ?? -1;

    private bool IsLastOfArray(int[] arr, int element) => arr.LastOrDefault() == element;

    public int GetNextRoomId(int roomId)
    {
        if (curRoomChapter?.roomIds == null)
            return -1;
        if (!IsRoomFinish(roomId))
        {
            return -1;
        }

        bool isFind = false;
        foreach (var kv in curRoomChapter.roomIds)
        {
            if (kv == roomId)
            {
                isFind = true;
                continue;
            }

            if (isFind && TableConfigManage.Instance.GetTableRoom(kv)?.isComingSoon == false)
                return kv;
        }
        return -1;
    }

    public bool IsRoomsFinished(int[] roomIds) => roomIds.All(IsRoomFinish);
    public bool IsRoomsUnlocked(int[] roomIds) => roomIds.Any(IsUnLock);

    public void UnLockRoom(int roomId)
    {
        storageHome.RoomData[CurRoomId].IsFinish = true;
        int nextRoomId = roomId < 0 ? throw new InvalidOperationException("当前地图完成了 找后续地图 没有后续地图") : roomId;

        SaveUnLockStorageRoom(nextRoomId);
        if (RoomResSystem.Instance.IsRoomResReady(nextRoomId))
        {
            CurRoomId = nextRoomId;
            EnterRoom(nextRoomId, true, b =>
            {
                EventBus.Dispatch(new TryOpenRoomEvent(roomId, 0));
            });
            return;
        }

        if (Application.internetReachability == NetworkReachability.NotReachable)
        {
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIPopupDownLoadFail);
            return;
        }
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIPopupDownLoad, nextRoomId);
    }

    private void SaveUnLockStorageRoom(int roomId)
    {
        storageHome ??= SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        if (storageHome.UnLockRoom.ContainsKey(roomId))
            return;

        storageHome.UnLockRoom.Add(roomId, roomId);
    }

    public int GetLastUnLockRoomId()
    {
        if (curRoomChapter == null)
            return GetRoomId();

        int lastUnLockId = -1;
        foreach (var id in curRoomChapter.roomIds)
        {
            if (!IsUnLock(id))
                continue;

            lastUnLockId = id;
        }

        return lastUnLockId < 0 ? GetRoomId() : lastUnLockId;
    }

    public bool IsUnLock(int roomId) => IsCollectRoom(roomId) ? IsCollectRoomUnLock(roomId) : storageHome?.UnLockRoom.ContainsKey(roomId) == true;

    public void Show() => _currentInRoom?.Show();
    public void Hide() => _currentInRoom?.Hide();

    public int GetRoomNodeCount(int roomId)
    {
        var roomNodes = TableConfigManage.Instance.GetTableRoomNode(roomId);
        if (roomNodes == null || roomNodes.Count == 0)
            return 0;

        return roomNodes.Count(a => !a.isRvGet);
    }

    public float GetRoomDecorationRate(int roomId, ref int decNum, ref int rvNum, bool ignoreUnlock = false)
    {
        decNum = rvNum = 0;
        if (!ignoreUnlock && !IsUnLock(roomId))
            return 0;

        var roomNodes = TableConfigManage.Instance.GetTableRoomNode(roomId);
        if (roomNodes == null || roomNodes.Count == 0)
            return 0f;

        if (!storageHome.RoomData.TryGetValue(roomId, out var storageRoom))
            return 0f;

        var rvRoomNode = roomNodes.Where(a => a.isRvGet).ToDictionary(a => a.id, a => a.id);
        int nodeCount = roomNodes.Count(a => !a.isRvGet);

        foreach (var kv in storageRoom.RoomNodes)
        {
            if (kv.Value.Status == (int)RoomItem.Status.Received)
            {
                if (!rvRoomNode.ContainsKey((int)kv.Value.Id))
                    decNum++;
                else
                    rvNum++;
            }
        }

        return 1.0f * decNum / nodeCount;
    }

    public int CanUnLockNewRoom(bool openUI = true)
    {
        if (storageHome == null)
            return -1;

        int lastRoomId = GetLastUnLockRoomId();
        int r =0;
        int y =0;
        if (!storageHome.RoomData.ContainsKey(lastRoomId) || !IsRoomFinish(lastRoomId))
            return -1;

        int nextRoomId = GetNextRoomId(lastRoomId);
        if (nextRoomId <= 0)
            return -1;

        if (openUI)
        {
            UIView_UnlockRoom.OpenData openData = new UIView_UnlockRoom.OpenData()
            {
                nextRoomId = nextRoomId,
                showUnlockView = true,
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_UnlockRoom, openData);
        }
        return nextRoomId;
    }

    private void UpdateAtlasPathNodeList(int roomId)
    {
        // AtlasPathNode atlasNode = AtlasConfigController.Instance.AtlasPathNodeList.Find((a) => { return a.AtlasName == roomId.ToString(); });
        // if (atlasNode == null)
        // {
        //     AtlasPathNode atlasPathNode = new AtlasPathNode();
        //     atlasPathNode.AtlasName = roomId.ToString();
        //     atlasPathNode.HdPath = string.Format(PathManager.ROOM_ATLAS_HD, roomId) + "/" + atlasPathNode.AtlasName;
        //     atlasPathNode.SdPath = string.Format(PathManager.ROOM_ATLAS_SD, roomId) + "/" + atlasPathNode.AtlasName;
        //     
        //     AtlasConfigController.Instance.AtlasPathNodeList.Add(atlasPathNode);
        // }
        //
        // atlasNode = AtlasConfigController.Instance.AtlasPathNodeList.Find((a) => { return a.AtlasName == string.Format(AtlasName.RoomIconFormat, roomId); });
        // if (atlasNode == null)
        // {
        //     AtlasPathNode atlasPathNode = new AtlasPathNode();
        //     atlasPathNode.AtlasName = string.Format(AtlasName.RoomIconFormat, roomId);
        //     atlasPathNode.HdPath = string.Format(PathManager.ROOM_ICONATLAS_HD, roomId)+ "/" + atlasPathNode.AtlasName;
        //     atlasPathNode.SdPath = string.Format(PathManager.ROOM_ICONATLAS_SD, roomId)+ "/" + atlasPathNode.AtlasName;
        //     
        //     AtlasConfigController.Instance.AtlasPathNodeList.Add(atlasPathNode);
        // }
    }

    public int GetCurrRoomIndex()
    {
        if (CurRoomChapter?.roomIds == null)
            return -1;

        return Array.IndexOf(CurRoomChapter.roomIds, CurRoomId);
    }

    public int GetCurRoomTotalIndex()
    {
        if (CurRoomChapter?.roomIds == null)
            return -1;

        var chapterCfgList = TableConfigManage.Instance.GetRoomChapters();
        int ret = 0;
        for (int i = 0; i < chapterCfgList.Count; i++)
        {
            if (chapterCfgList[i].id == CurRoomChapter.id)
            {
                ret += GetCurrRoomIndex();
                break;
            }
            ret += chapterCfgList[i].roomIds.Length;
        }
        return ret;
    }

    public void ReplaceRoomNode(bool isForce)
    {
        if (curCharpterId != roomChapterB)
            return;

        TableConfigManage.Instance.ReplaceRoomNode(isForce);
    }

    #region ABTestDebug

    public void DebugUpdateRoomChapterStorage()
    {
        // storageHome.RoomChapterId = ABTestManager.Instance.IsRoomTestB() ? roomChapterB : roomChapter;
        UpdateRoomChapterStorage();
    }

    #endregion
}
