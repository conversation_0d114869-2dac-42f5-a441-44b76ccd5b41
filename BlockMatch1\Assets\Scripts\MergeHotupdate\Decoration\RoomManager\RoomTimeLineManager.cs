using System;
using System.Collections;
using DecorationRom;
using Framework;
using Gameplay;
using TMGame;
using UnityEngine;


public class RoomTimeLineManager : Manager<RoomTimeLineManager>
{
   public bool isPlayAnim = false;
   private Action animCallAction = null;
   private TableRoomAnimTimeLine tableConfig = null;
   
   public void PlayRoomAnim(int roomId, Action callBack = null)
   {
      if(isPlayAnim)
         StopRoomAnim();
      
      InitData();
      
      tableConfig = TableConfigManage.Instance.GetRoomAnimTimeLine(roomId);
      if (tableConfig == null)
      {
        
         CLog.Error("RoomAnim null " + roomId);
         return;
      }

      animCallAction = callBack;
      isPlayAnim = true;
      StopAllCoroutines();
      StartCoroutine(PlayRoomAnim(tableConfig, callBack));

      
      float time =  RoomSound.PlaySound(tableConfig.sound);
      if (time > 0)
      {
          RoomSound.PauseAllMusic();
          GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(time, () =>
         {
             RoomSound.ResumeAllMusic();
            if (tableConfig != null && tableConfig.starSfx > 0)
            {
               time =  RoomSound.PlaySound(tableConfig.starSfx);
               if (time > 0)
               {
                   GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(time, RoomSound.ResumeAllMusic));
               }
               else
               {
                   RoomSound.ResumeAllMusic();
               }
            }
            
         }));
      }
   }

   private IEnumerator PlayRoomAnim(TableRoomAnimTimeLine tableConfig, Action callBack = null)
   {
      float[] timeLine = null;
      float totalTime = 0;

      timeLine = tableConfig.showTimeLine;

      if (timeLine == null || timeLine.Length == 0)
      {
         if (callBack != null)
            callBack();

         InitData();
         yield break;
      }
      
      RoomManager.Instance.StopEffect();
      RoomManager.Instance.ClearAllGraphic();
      RoomManager.Instance.ShowAllOldItem();
      
      for (int i = 0; i < timeLine.Length; i++)
      {
         int index = i;
         if(tableConfig.showRoomNode.Length <= index)
            break;
            
         StartCoroutine(DelayFunction(timeLine[i], () =>
         {
            RoomManager.Instance.GetRoomNode(tableConfig.showRoomNode[index], true, false);
         }));
      }

      yield return new WaitForSeconds(tableConfig.showTime);
      
      // PlayRoomEffect();
      //
      // yield return new WaitForSeconds(1.5f);
      
      if (callBack != null)
         callBack();

      InitData();
      
      if(tableConfig != null)
          RoomSound.StopSoundById(tableConfig.sound);
      
       RoomSound.ResumeAllMusic();
   }

   private IEnumerator DelayFunction(float time, Action action)
   {
      yield return new WaitForSeconds(time);

      if (action != null)
         action();
   }
   public void StopRoomAnim(bool isRest = false)
   {
      if(!isPlayAnim)
         return;
      
      StopAllCoroutines();
      
      if(tableConfig != null)
          RoomSound.StopSoundById(tableConfig.sound);
      
      if(isRest && tableConfig != null && tableConfig.showRoomNode.Length > 0)
      {
         for (int i = 0; i < tableConfig.showRoomNode.Length; i++)
         {
            int id = tableConfig.showRoomNode[i];
            RoomManager.Instance.GetRoomNode(id, false, false);
         }
      }
      
      if (animCallAction != null)
         animCallAction();
      
      InitData();
      
       RoomSound.ResumeAllMusic();
   }

   private void InitData()
   {
      animCallAction = null;
      isPlayAnim = false;
      tableConfig = null;
   }
   
}
