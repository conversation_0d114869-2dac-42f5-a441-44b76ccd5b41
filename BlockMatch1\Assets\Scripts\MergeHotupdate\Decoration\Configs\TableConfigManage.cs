using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using DragonPlus.Core;
using DragonU3DSDK;
using Framework;
using Home.Core;
using Newtonsoft.Json;
using DecorationRom.Config;
using DragonPlus.Save;
using TMGame;
using TMGame.Storage;
using UnityEngine;

public class TableConfigManage : Singleton<TableConfigManage>
{
    private Dictionary<int, TableRoom> tableRoomConfigs = new Dictionary<int, TableRoom>();
    private Dictionary<int, List<TableRoomNode>> tableRoomNodeConfigs = new Dictionary<int, List<TableRoomNode>>();
    private Dictionary<int, Home.Core.HomeViewConfig> _roomNodeViewCfg = new Dictionary<int, HomeViewConfig>();
    private List<TableRoomNode> replaceRoomNodeConfigs = new List<TableRoomNode>();
    private Dictionary<int, TableRoomNode> _roomNodeDict = new Dictionary<int, TableRoomNode>();
    private Dictionary<int, TableRoomChapter> _roomChapterDict = new Dictionary<int, TableRoomChapter>();
    private Dictionary<int, TableRewards> tableRewardsDic = new Dictionary<int, TableRewards>();

    private List<TableRoomChapter> tableRoomChapters = new List<TableRoomChapter>();
    private List<TableCollectRoomChapter> tableCollectRoomChapters = new List<TableCollectRoomChapter>();
    private Dictionary<int, TableRoomAnimTimeLine> tableAnimTimeLines = new Dictionary<int, TableRoomAnimTimeLine>();

    public int MaxChaptersId;
    private bool isReplace = false;
    public void InitTableConfigs()
    {
        TableManager.Instance.InitLocation("configs");
        InitTable<TableRoom>(tableRoomConfigs);
        InitTable<TableRoomNode>(tableRoomNodeConfigs);
        InitTable<TableRoomNode>(_roomNodeDict);
        InitTable<TableRoomChapter>(_roomChapterDict);
        InitTable<TableRewards>(tableRewardsDic);

        InitTable(tableAnimTimeLines);
        tableRoomChapters = TableManager.Instance.GetTable<TableRoomChapter>();
        tableCollectRoomChapters = TableManager.Instance.GetTable<TableCollectRoomChapter>();
        replaceRoomNodeConfigs = TableManager.Instance.GetTable<TableRoomNode>();
        // TextAsset json = Game.Mgr<ResMgr>().GetRes<TextAsset>("room_replaceRoomNode".ToLower()).GetInstance(GameGlobal.DontDestoryRoot);
        // if (json != null)
        // {
        //     replaceRoomNodeConfigs = TableManager.DeSerialize<TableRoomNode>(json?.text);
        // }

        InitChapterIds();
        MaxChaptersId = tableRoomChapters.Count;
    }

    public void Dispose()
    {
    }

    private Dictionary<int, int> chapterIdsMap = new Dictionary<int, int>();

    public int GetDefaultRoomId()
    {
        return 70039;
    }

    public int GetFirstGuideRoomNode()
    {
        return 7001699;
    }

    private void InitChapterIds()
    {
        chapterIdsMap.Clear();
        for (int i = 0; i < tableRoomChapters.Count; i++)
        {
            var areaConf = tableRoomChapters[i];
            foreach (var item in areaConf.roomIds)
            {
                chapterIdsMap.TryAdd(item, areaConf.id);
            }
        }
    }

    public int GetRoomChapterId(int roomId)
    {
        var v = chapterIdsMap.GetValueOrDefault(roomId);
        return chapterIdsMap.GetValueOrDefault(roomId);
    }

    public int GetRoomIndex(int roomId)
    {
        int areaId = GetRoomChapterId(roomId);
        var data = _roomChapterDict.GetValueOrDefault(areaId);
        if (null == data || data.roomIds == null)
        {
            return 0;
        }
        return Array.IndexOf(data.roomIds, roomId) + 1;
    }

    public TableRoomChapter GetRoomList(int areaId)
    {
        return _roomChapterDict.GetValueOrDefault(areaId);
    }

    private void InitTable<T>(Dictionary<int, T> config) where T : TableBase
    {
        if (config == null)
            return;

        List<T> tableData = TableManager.Instance.GetTable<T>();
        if (tableData == null)
            return;

        config.Clear();
        foreach (T kv in tableData)
        {
            config.Add(kv.GetID(), kv);
        }
    }

    private void InitTable<T>(Dictionary<int, List<T>> config) where T : TableRoomNode
    {
        if (config == null)
            return;

        List<T> tableData = TableManager.Instance.GetTable<T>();
        if (tableData == null)
            return;

        config.Clear();
        List<T> listData = null;

        foreach (T kv in tableData)
        {
            if (config.ContainsKey(kv.roomId))
                listData = config[kv.roomId];
            else
            {
                listData = new List<T>();
                config.Add(kv.roomId, listData);
            }

            listData.Add(kv);
        }
    }

    public List<TableRoomChapter> GetTableRoomList()
    {
        return tableRoomChapters;
    }

    private void InitTable(Dictionary<int, TableRoomAnimTimeLine> config)
    {
        if (config == null)
            return;

        List<TableRoomAnimTimeLine> tableData = TableManager.Instance.GetTable<TableRoomAnimTimeLine>();
        if (tableData == null)
            return;

        config.Clear();

        foreach (TableRoomAnimTimeLine kv in tableData)
        {
            if (config.ContainsKey(kv.roomId))
                config[kv.roomId] = kv;
            else
            {
                config.Add(kv.roomId, kv);
            }
            if (kv.showTimeLine == null || kv.showRoomNode == null)
            {
                CLog.Error($"严重错误！{kv.roomId}房间时间线没有配置！");
                continue;
            }

            //错误改成警告，现在允许时间轴数量多于节点
            if (kv.showTimeLine.Length != kv.showRoomNode.Length)
                CLog.Warning("Show Length Error" + kv.roomId);
        }
    }

    public TableRewards GetRoomRewards(int id)
    {
        return tableRewardsDic.GetValueOrDefault(id);
    }

    public TableRoom GetTableRoom(int id)
    {
        if (tableRoomConfigs == null || tableRoomConfigs.Count == 0)
            return null;

        if (!tableRoomConfigs.ContainsKey(id))
            return null;

        return tableRoomConfigs[id];
    }


    public int GetRoomResId(int id)
    {
        TableRoom tableRoom = GetTableRoom(id);
        if (tableRoom == null)
            return -1;

        return tableRoom.roomResId;
    }

    public List<TableRoomNode> GetTableRoomNode(int id)
    {
        if (tableRoomNodeConfigs == null || tableRoomNodeConfigs.Count == 0)
            return null;

        if (!tableRoomNodeConfigs.ContainsKey(id))
            return null;

        return tableRoomNodeConfigs[id];
    }

    public TableRoomNode GetRoomNode(int id)
    {
        if (_roomNodeDict.ContainsKey(id) == false)
            return null;
        return _roomNodeDict[id];
    }

    public Home.Core.HomeViewConfig GetRoomNodeViewConfig(int roomResId)
    {
        try
        {
            Home.Core.HomeViewConfig result = null;
            if (!_roomNodeViewCfg.TryGetValue(roomResId, out result))
            {
                result = new Home.Core.HomeViewConfig(
                    ((int)Home.Core.HomeViewConfigType.Base |
                     (int)Home.Core.HomeViewConfigType.Offset |
                     (int)Home.Core.HomeViewConfigType.OldFur |
                     (int)Home.Core.HomeViewConfigType.Anim)
                    , roomResId.ToString(), "royalx");
                _roomNodeViewCfg.Add(roomResId, result);
            }
            return result;
        }
        catch (Exception e)
        {
            CLog.Error(e);
        }
        return null;
    }

    public List<TableRoomChapter> GetRoomChapters()
    {
        return tableRoomChapters;
    }

    public List<TableCollectRoomChapter> GetCollectRoomChapters()
    {
        return tableCollectRoomChapters;
    }

    public TableRoomAnimTimeLine GetRoomAnimTimeLine(int roomId)
    {
        if (tableAnimTimeLines == null || tableAnimTimeLines.Count == 0)
            return null;

        if (!tableAnimTimeLines.ContainsKey(roomId))
            return null;

        return tableAnimTimeLines[roomId];
    }

    public void ReplaceRoomNode(bool isForce = false)
    {
        if (isReplace && !isForce)
            return;

        isReplace = true;
        foreach (var kv in replaceRoomNodeConfigs)
        {
            if (tableRoomNodeConfigs.ContainsKey(kv.roomId))
                tableRoomNodeConfigs.Remove(kv.roomId);
        }

        List<TableRoomNode> listData = null;
        foreach (var kv in replaceRoomNodeConfigs)
        {
            if (tableRoomNodeConfigs.ContainsKey(kv.roomId))
                listData = tableRoomNodeConfigs[kv.roomId];
            else
            {
                listData = new List<TableRoomNode>();
                tableRoomNodeConfigs.Add(kv.roomId, listData);
            }

            listData.Add(kv);
        }
    }
}