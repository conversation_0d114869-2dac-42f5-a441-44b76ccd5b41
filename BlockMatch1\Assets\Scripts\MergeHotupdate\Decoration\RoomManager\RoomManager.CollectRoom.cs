using System.Collections;
using System.Collections.Generic;
using DragonPlus.Core;
using DragonPlus.Save;
using TMGame;
using TMGame.Storage;

public partial class RoomManager
{
    Dictionary<int, int> _collectRoomDict = new Dictionary<int, int>();
    private TableCollectRoomChapter curCollectRoomChapter = null;
    public TableCollectRoomChapter CurCollectRoomChapter { get { return curCollectRoomChapter; } }

    public int GetCurRoomNodeId()
    {
        StorageRoom storageRoom = RoomManager.Instance.GetStorageRoom();
        foreach (var kv in storageRoom.RoomNodes)
        {
            if (kv.Value.Status != (int) RoomItem.Status.UnLock)
                continue;
            int id = (int) kv.Value.Id;

            TableRoomNode tableRoomNode = RoomManager.Instance.CurrentInRoom.Data.GetRoomNode(id);
            if(tableRoomNode == null)
                continue;
            return tableRoomNode.id;
        }
        return 0;
    }
    
    public void UpdateCollectRoomChapterStorage()
    {
        storageHome = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        if (storageHome == null)
            return;

        if (storageHome.CollectRoomChapterId <= 0)
            storageHome.CollectRoomChapterId = 9001;

        int charpterId = storageHome.CollectRoomChapterId;
        curCollectRoomChapter = null;
        //curCharpterId = charpterId;

        ReplaceRoomNode(false);

        List<TableCollectRoomChapter> listRoomChapters = TableConfigManage.Instance.GetCollectRoomChapters();
        if (listRoomChapters == null || listRoomChapters.Count == 0)
            return;

        foreach (var kv in listRoomChapters)
        {
           
            if (kv.id != charpterId)
                continue;

            curCollectRoomChapter = kv;
        }

        if (curCollectRoomChapter == null)
            curCollectRoomChapter = listRoomChapters[^1];

        if (CurCollectRoomChapter == null || CurCollectRoomChapter.roomIds == null)
            return;
        for (int i = 0; i < CurCollectRoomChapter.roomIds.Length; i++)
        {
            _collectRoomDict[CurCollectRoomChapter.roomIds[i]] = CurCollectRoomChapter.roomIds[i];
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public int GetFirstCollectRoomId()
    {
        if (CurCollectRoomChapter == null || CurCollectRoomChapter.roomIds == null || CurCollectRoomChapter.roomIds.Length == 0)
            return -1;

        return CurCollectRoomChapter.roomIds[0];
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="roomId"></param>
    /// <returns></returns>
    public int GetNextCollectRoomChapter(int roomId)
    {
        if (CurCollectRoomChapter == null || CurCollectRoomChapter.roomIds == null || CurCollectRoomChapter.roomIds.Length == 0)
            return -1;

        bool isFind = false;
        foreach (var kv in CurCollectRoomChapter.roomIds)
        {
            if (kv == roomId)
            {
                isFind = true;
                continue;
            }

            if (isFind)
            {
                TableRoom roomConfig = TableConfigManage.Instance.GetTableRoom(kv);
                if (roomConfig != null)
                {
                    if (!roomConfig.isComingSoon)
                        return kv;
                }
            }
        }

        return -1;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="roomId"></param>
    /// <returns></returns>
    public bool IsCollectRoom(int roomId)
    {
        return _collectRoomDict.ContainsKey(roomId);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public bool IsCollectRoomOpen()
    {
        //todo 代码清理
        return false;
        // return ActivityCollectRoomManager.Instance.IsCollectRoomOpen();
    }

    public bool IsActiveRoom(int roomId)
    {
        //todo 代码清理
        return false;
        // return ActivityCollectRoomManager.Instance.IsActiveRoom(roomId);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="roomId"></param>
    /// <returns></returns>
    public bool IsCollectRoomUnLock(int roomId)
    {
        int progres = (int)(GetRoomProgress(roomId) * 100);
        if (progres > 0)
            return true;
        int keyCount = GameGlobal.GetMod<ModBag>().GetItemCount(EItemType.CollectKey);
        if (keyCount > 0)
            return true;
        //todo 代码清理
        return false;
        // return ActivityCollectRoomManager.Instance.IsActiveRoom(roomId);
    }
}
